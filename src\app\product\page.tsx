'use client';

import { useEffect, useState } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../firebase/config';
import ProductCard from './component/ProductCard';
import { Item } from '../types';

const ProductPage = () => {
  const [allProducts, setAllProducts] = useState<Item[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Item[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10); // Number of products per page

  // Fetch all products once when component mounts
  useEffect(() => {
    const fetchAllProducts = async () => {
      setLoading(true);
      setError(null);
      try {
        const productsCollection = collection(db, 'products');
        const querySnapshot = await getDocs(productsCollection);
        const productList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as Item[];

        setAllProducts(productList);
        setFilteredProducts(productList); // Initially show all products
      } catch (error) {
        console.error('ไม่สามารถโหลดข้อมูลสินค้าได้:', error);

        // Set user-friendly error message
        let errorMessage = 'ไม่สามารถโหลดข้อมูลสินค้าได้ กรุณาลองใหม่อีกครั้ง';

        // Type guard for error with code property
        function hasCodeProperty(err: unknown): err is { code: string } {
          return typeof err === 'object' && err !== null && 'code' in err && typeof (err as { code: unknown }).code === 'string';
        }

        if (hasCodeProperty(error)) {
          const errorCode = error.code;
          if (errorCode === 'permission-denied') {
            errorMessage = 'ไม่มีสิทธิ์เข้าถึงข้อมูล กรุณาเข้าสู่ระบบใหม่';
          } else if (errorCode === 'unavailable') {
            errorMessage = 'เซิร์ฟเวอร์ไม่พร้อมใช้งาน กรุณาลองใหม่ในภายหลัง';
          }
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchAllProducts();
  }, []);

  // Filter products when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      // If search is empty, show all products
      setFilteredProducts(allProducts);
      return;
    }

    // Filter products by name or details (case insensitive)
    const lowercaseSearchTerm = searchTerm.toLowerCase();
    const filtered = allProducts.filter((product) => {
      // Safely check if properties exist before calling toLowerCase()
      const productName = product.productName || '';
      const productDetails = product.productDetails || '';
      const productType = product.productType || '';

      const nameMatch = productName.toLowerCase().includes(lowercaseSearchTerm);
      const detailsMatch = productDetails.toLowerCase().includes(lowercaseSearchTerm);
      const typeMatch = productType.toLowerCase().includes(lowercaseSearchTerm);

      return nameMatch || detailsMatch || typeMatch;
    });

    setFilteredProducts(filtered);
  }, [searchTerm, allProducts]);

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
  };

  // Calculate pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProducts.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Go to next page
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Go to previous page
  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5; // Show maximum 5 page numbers

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return pageNumbers;
  };

  return (
    <div className="flex flex-col p-2 bg-gray-100">
      <div className="relative w-full max-w-xl mx-auto mb-8">
        <input
          type="text"
          placeholder="ค้นหาสินค้า..."
          value={searchTerm}
          onChange={handleSearchChange}
          className={`search-input p-2 px-4 border ${searchTerm ? 'border-blue-400' : 'border-gray-300'} rounded-full w-full outline-none shadow-md focus:ring focus:ring-blue-200`}
        />
        {searchTerm && (
          <button
            onClick={() => setSearchTerm('')}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        )}
      </div>

      {error && (
        <div className="text-red-500 text-center mb-4">{error}</div>
      )}

      {loading ? (
        <div className="text-center py-8">กำลังโหลด...</div>
      ) : filteredProducts.length > 0 ? (
        <>
          {searchTerm && (
            <div className="text-center mb-4 text-sm text-gray-600">
              พบ {filteredProducts.length} รายการ สำหรับ &ldquo;{searchTerm}&rdquo;
            </div>
          )}
          <div className="flex flex-wrap gap-4 justify-center">
            {currentItems.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8 mb-4">
              <nav className="flex items-center">
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`px-3 py-1 mx-1 rounded ${
                    currentPage === 1
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-white text-blue-500 hover:bg-blue-50'
                  }`}
                >
                  ก่อนหน้า
                </button>

                {getPageNumbers().map(number => (
                  <button
                    key={number}
                    onClick={() => paginate(number)}
                    className={`px-3 py-1 mx-1 rounded ${
                      currentPage === number
                        ? 'bg-blue-500 text-white'
                        : 'bg-white text-blue-500 hover:bg-blue-50'
                    }`}
                  >
                    {number}
                  </button>
                ))}

                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`px-3 py-1 mx-1 rounded ${
                    currentPage === totalPages
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-white text-blue-500 hover:bg-blue-50'
                  }`}
                >
                  ถัดไป
                </button>
              </nav>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-8 text-gray-500">
          ไม่พบสินค้าที่ค้นหา &ldquo;{searchTerm}&rdquo;
        </div>
      )}
    </div>
  );
};

export default ProductPage;