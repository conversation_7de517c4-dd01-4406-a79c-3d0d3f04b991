import React, { useState } from 'react';
import { db, auth } from '../firebase/config';
import { collection, getDocs, addDoc } from 'firebase/firestore';

const FirebaseTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const testFirebaseConnection = async () => {
    setLoading(true);
    setTestResult('');
    
    try {
      // Test 1: Check authentication
      console.log('Testing authentication...');
      const user = auth.currentUser;
      if (!user) {
        setTestResult('❌ ไม่ได้เข้าสู่ระบบ - กรุณาเข้าสู่ระบบก่อน');
        setLoading(false);
        return;
      }
      
      console.log('✅ Authentication OK - User:', user.email);
      
      // Test 2: Try to read from products collection
      console.log('Testing Firestore read...');
      const productsRef = collection(db, 'products');
      const snapshot = await getDocs(productsRef);
      
      console.log('✅ Firestore read OK - Documents:', snapshot.size);
      
      // Test 3: Try to write to products collection (test document)
      console.log('Testing Firestore write...');
      const testDoc = {
        name: 'Test Product',
        price: '100',
        timestamp: new Date().toISOString(),
        isTest: true
      };
      
      await addDoc(productsRef, testDoc);
      console.log('✅ Firestore write OK');
      
      setTestResult(`✅ การทดสอบสำเร็จ!
      
🔐 Authentication: ผ่าน (${user.email})
📖 Firestore Read: ผ่าน (พบ ${snapshot.size} เอกสาร)
✏️ Firestore Write: ผ่าน
      
ระบบพร้อมใช้งาน!`);
      
    } catch (error) {
      console.error('Firebase test failed:', error);

      let errorMessage = '❌ การทดสอบล้มเหลว: ';

      // Type guard for error with code/message property
      function hasCodeAndMessage(err: unknown): err is { code?: string; message?: string } {
        return typeof err === 'object' && err !== null;
      }

      if (hasCodeAndMessage(error)) {
        if (error.code === 'permission-denied') {
          errorMessage += 'ไม่มีสิทธิ์เข้าถึงข้อมูล - ตรวจสอบ Firestore Rules';
        } else if (error.code === 'unavailable') {
          errorMessage += 'เซิร์ฟเวอร์ไม่พร้อมใช้งาน';
        } else if (typeof error.message === 'string' && error.message.includes('network')) {
          errorMessage += 'ปัญหาการเชื่อมต่อเครือข่าย';
        } else {
          errorMessage += error.message || 'ข้อผิดพลาดไม่ทราบสาเหตุ';
        }
      } else {
        errorMessage += 'ข้อผิดพลาดไม่ทราบสาเหตุ';
      }

      setTestResult(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">🔧 ทดสอบการเชื่อมต่อ Firebase</h3>
      
      <button
        onClick={testFirebaseConnection}
        disabled={loading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400 mb-4"
      >
        {loading ? 'กำลังทดสอบ...' : 'ทดสอบการเชื่อมต่อ'}
      </button>
      
      {testResult && (
        <div className="mt-4 p-4 border rounded bg-white">
          <pre className="whitespace-pre-wrap text-sm">{testResult}</pre>
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-600">
        <p><strong>การทดสอบจะตรวจสอบ:</strong></p>
        <ul className="list-disc list-inside mt-2">
          <li>สถานะการเข้าสู่ระบบ</li>
          <li>การอ่านข้อมูลจาก Firestore</li>
          <li>การเขียนข้อมูลลง Firestore</li>
          <li>สิทธิ์การเข้าถึงข้อมูล</li>
        </ul>
      </div>
    </div>
  );
};

export default FirebaseTest;
